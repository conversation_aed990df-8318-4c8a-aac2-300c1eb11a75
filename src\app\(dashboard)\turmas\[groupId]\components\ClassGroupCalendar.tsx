'use client';

import { useState, useEffect, useTransition, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar as CalendarIcon, 
  Clock, 
  Users, 
  MapPin,
  ChevronLeft, 
  ChevronRight,
  Plus,
  RefreshCw,
  Eye,
  Edit3,
  UserCheck,
  AlertCircle,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { getClassesByGroup } from '@/app/(dashboard)/aulas/actions';
import { ClassGroupWithDetails } from '@/app/(dashboard)/aulas/types';
import { 
  format, 
  isToday, 
  isSameMonth, 
  startOfMonth, 
  endOfMonth, 
  eachDayOfInterval, 
  startOfWeek, 
  endOfWeek, 
  addMonths, 
  subMonths,
  parseISO,
  isSameDay
} from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ClassGroupCalendarProps {
  classGroup?: ClassGroupWithDetails;
}

interface CalendarEvent {
  id: string;
  title: string;
  startTime: Date;
  endTime: Date;
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  attendanceCount?: number;
  maxCapacity?: number;
  instructor?: string;
  location?: string;
}

interface CalendarDay {
  date: Date;
  dateStr: string;
  isCurrentMonth: boolean;
  isToday: boolean;
  events: CalendarEvent[];
  day: number;
}

export function ClassGroupCalendar({ classGroup }: ClassGroupCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();


  // Carregar eventos do calendário
  const loadCalendarEvents = async () => {
    if (!classGroup?.id) return;

    setIsLoading(true);
    setError(null);
    try {
      const result = await getClassesByGroup(classGroup.id);
      
      if (result.success && result.data && result.data.data && Array.isArray(result.data.data)) {
        const calendarEvents: CalendarEvent[] = result.data.data.map((classItem: any) => {
          const startTime = parseISO(classItem.start_time);
          const endTime = parseISO(classItem.end_time);
          const now = new Date();
          
          // Determinar status real baseado na data/hora atual (usando timezone de Brasília)
          const nowBrasilia = new Date(new Date().toLocaleString("en-US", {timeZone: "America/Sao_Paulo"}));
          const startTimeBrasilia = new Date(startTime.toLocaleString("en-US", {timeZone: "America/Sao_Paulo"}));
          const endTimeBrasilia = new Date(endTime.toLocaleString("en-US", {timeZone: "America/Sao_Paulo"}));
          
          let actualStatus = classItem.status as CalendarEvent['status'];
          
          // Se a aula está agendada, verificar se está em andamento agora
          if (classItem.status === 'scheduled') {
            if (nowBrasilia >= startTimeBrasilia && nowBrasilia <= endTimeBrasilia) {
              actualStatus = 'ongoing';
            }
          }
          
          return {
            id: classItem.id,
            title: classItem.name || `Aula - ${classGroup.name}`,
            startTime: startTime,
            endTime: endTime,
            status: actualStatus,
            attendanceCount: classItem._count?.attendance || 0,
            maxCapacity: classGroup.max_capacity || undefined,
            instructor: classItem.instructor?.first_name && classItem.instructor?.last_name 
              ? `${classItem.instructor.first_name} ${classItem.instructor.last_name}`
              : 'Instrutor não definido',
            location: classItem.location || classGroup.branch?.name || 'Local não definido'
          };
        });

        setEvents(calendarEvents);
        
        // Navegar automaticamente para o primeiro mês que tem eventos
        if (calendarEvents.length > 0) {
          const firstEvent = calendarEvents.sort((a, b) => a.startTime.getTime() - b.startTime.getTime())[0];
          setCurrentDate(firstEvent.startTime);
        }
        
        // Toast de sucesso apenas no primeiro carregamento
        if (events.length === 0) {
          toast.success("Calendário carregado", {
            description: `${calendarEvents.length} aula${calendarEvents.length !== 1 ? 's' : ''} encontrada${calendarEvents.length !== 1 ? 's' : ''}`,
          });
        }
      } else {
        setEvents([]);
        if (!result.success) {
          setError(result.errors?._form || 'Erro ao carregar eventos do calendário');
        }
      }
    } catch (error) {
      console.error('Erro ao carregar eventos do calendário:', error);
      setError('Erro inesperado ao carregar o calendário. Tente novamente.');
      setEvents([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadCalendarEvents();
  }, [classGroup?.id]);

  // Agrupar eventos por data
  const eventsByDate = useMemo(() => {
    const grouped = events.reduce((acc: Record<string, CalendarEvent[]>, event) => {
      const dateStr = format(event.startTime, 'yyyy-MM-dd');
      
      if (!acc[dateStr]) {
        acc[dateStr] = [];
      }
      acc[dateStr].push(event);
      return acc;
    }, {});
    
    return grouped;
  }, [events]);

  // Gerar grid do calendário
  const generateMonthGrid = (): CalendarDay[] => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const calendarStart = startOfWeek(monthStart, { weekStartsOn: 0 });
    const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 0 });
    
    const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd });
    
    const grid = days.map(date => {
      const dateStr = format(date, 'yyyy-MM-dd');
      const dayEvents = eventsByDate[dateStr] || [];
      
      return {
        date,
        dateStr,
        isCurrentMonth: isSameMonth(date, currentDate),
        isToday: isToday(date),
        events: dayEvents,
        day: date.getDate()
      };
    });
    
    return grid;
  };

  // Navegação do calendário
  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = direction === 'next' 
      ? addMonths(currentDate, 1) 
      : subMonths(currentDate, 1);
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    const today = new Date();
    setCurrentDate(today);
    setSelectedDate(today);
  };

  // Refresh dos dados
  const handleRefresh = () => {
    startTransition(() => {
      loadCalendarEvents().then(() => {
        toast.success("Calendário atualizado", {
          description: "Os dados foram atualizados com sucesso",
        });
      }).catch(() => {
        toast.error("Erro ao atualizar", {
          description: "Não foi possível atualizar o calendário",
        });
      });
    });
  };

  // Obter eventos do dia selecionado
  const selectedDateEvents = selectedDate 
    ? eventsByDate[format(selectedDate, 'yyyy-MM-dd')] || []
    : [];

  // Styling functions
  const getEventStatusColor = (status: CalendarEvent['status']) => {
    const colors = {
      'scheduled': 'bg-blue-100 dark:bg-blue-900/50 border-blue-300 dark:border-blue-700 text-blue-800 dark:text-blue-200',
      'ongoing': 'bg-orange-100 dark:bg-orange-900/50 border-orange-300 dark:border-orange-700 text-orange-800 dark:text-orange-200',
      'completed': 'bg-green-100 dark:bg-green-900/50 border-green-300 dark:border-green-700 text-green-800 dark:text-green-200',
      'cancelled': 'bg-red-100 dark:bg-red-900/50 border-red-300 dark:border-red-700 text-red-800 dark:text-red-200'
    };
    return colors[status];
  };

  const getEventStatusIcon = (status: CalendarEvent['status']) => {
    const icons = {
      'scheduled': <Clock className="h-3 w-3" />,
      'ongoing': <AlertCircle className="h-3 w-3" />,
      'completed': <CheckCircle className="h-3 w-3" />,
      'cancelled': <XCircle className="h-3 w-3" />
    };
    return icons[status];
  };

  const getEventStatusLabel = (status: CalendarEvent['status']) => {
    switch (status) {
      case 'scheduled': return 'Agendada';
      case 'ongoing': return 'Em andamento';
      case 'completed': return 'Concluída';
      case 'cancelled': return 'Cancelada';
      default: return 'Desconhecido';
    }
  };

  const monthDays = generateMonthGrid();

  // Componente de skeleton para loading
  const CalendarSkeleton = () => (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Calendário principal skeleton */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader className="pb-4">
            <Skeleton className="h-6 w-32 mx-auto" />
          </CardHeader>
          <CardContent className="p-6">
            {/* Headers dos dias da semana */}
            <div className="grid grid-cols-7 gap-2 mb-4">
              {Array.from({ length: 7 }).map((_, i) => (
                <Skeleton key={i} className="h-6 w-8 mx-auto" />
              ))}
            </div>
            
            {/* Grid do calendário skeleton */}
            <div className="grid grid-cols-7 gap-2">
              {Array.from({ length: 35 }).map((_, i) => (
                <div key={i} className="min-h-24 p-2 border rounded-lg">
                  <Skeleton className="h-4 w-6 mb-2" />
                  <div className="space-y-1">
                    {i % 3 === 0 && (
                      <Skeleton className="h-8 w-full rounded" />
                    )}
                    {i % 5 === 0 && (
                      <Skeleton className="h-8 w-full rounded" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Painel lateral skeleton */}
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="p-3 border rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-5 w-16" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-3 w-20" />
                    <Skeleton className="h-3 w-32" />
                    <Skeleton className="h-3 w-28" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // Loading inicial
  if (isLoading && events.length === 0) {
    return (
      <div className="space-y-6">
        {/* Header com skeleton */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div>
            <Skeleton className="h-6 w-40 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-9 w-20" />
            <Skeleton className="h-9 w-20" />
            <Skeleton className="h-9 w-9" />
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-28" />
          </div>
        </div>
        
        <CalendarSkeleton />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Mensagem de erro */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{error}</span>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                setError(null);
                loadCalendarEvents();
              }}
              className="ml-4"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Tentar novamente
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Header com navegação */}
      <div className="flex flex-col space-y-3 sm:space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div>
          <div className="flex items-center gap-2">
            <h3 className="text-base sm:text-lg font-semibold text-slate-900 dark:text-gray-100">
              Calendário da Turma
            </h3>
            {isLoading && events.length > 0 && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <RefreshCw className="h-3 w-3 animate-spin" />
                <span className="hidden sm:inline">Atualizando...</span>
              </div>
            )}
          </div>
          <p className="text-xs sm:text-sm text-slate-600 dark:text-gray-400">
            Visualize as aulas agendadas para esta turma
          </p>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-2">
          {/* Navegação de mês */}
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth('prev')}
              disabled={isLoading}
              className="h-8 px-2 sm:h-9 sm:px-3"
            >
              <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={goToToday}
              disabled={isLoading}
              className="h-8 px-2 sm:h-9 sm:px-3 text-xs sm:text-sm"
            >
              Hoje
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateMonth('next')}
              disabled={isLoading}
              className="h-8 px-2 sm:h-9 sm:px-3"
            >
              <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4" />
            </Button>
          </div>

          {/* Refresh */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading || isPending}
            className="h-8 px-2 sm:h-9 sm:px-3"
          >
            <RefreshCw className={`h-3 w-3 sm:h-4 sm:w-4 ${(isLoading || isPending) ? 'animate-spin' : ''}`} />
          </Button>
          
          {/* Ir para mês com eventos */}
          {events.length > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                const firstEvent = events.sort((a, b) => a.startTime.getTime() - b.startTime.getTime())[0];
                setCurrentDate(firstEvent.startTime);
                setSelectedDate(firstEvent.startTime);
              }}
              disabled={isLoading}
            >
              <CalendarIcon className="h-4 w-4 mr-1" />
              Ir para eventos
            </Button>
          )}
          
          {/* Nova aula */}
          {classGroup && (
            <Button size="sm" asChild>
              <Link href={`/turmas/${classGroup.id}/aulas/nova`}>
                <Plus className="h-4 w-4 mr-1" />
                Nova Aula
              </Link>
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* Calendário principal */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-3 sm:pb-4 px-3 sm:px-6 pt-3 sm:pt-6">
              <CardTitle className="text-center text-base sm:text-lg">
                {format(currentDate, 'MMMM yyyy', { locale: ptBR })}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3 sm:p-6">
              {/* Estado vazio quando não há eventos */}
              {events.length === 0 && !isLoading && (
                <div className="text-center py-12">
                  <CalendarIcon className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    Nenhuma aula agendada
                  </h3>
                  <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                    Esta turma ainda não possui aulas agendadas. Comece criando a primeira aula.
                  </p>
                  {classGroup && (
                    <Button asChild>
                      <Link href={`/turmas/${classGroup.id}/aulas/nova`}>
                        <Plus className="h-4 w-4 mr-2" />
                        Criar primeira aula
                      </Link>
                    </Button>
                  )}
                </div>
              )}

              {/* Calendário normal quando há eventos */}
              {events.length > 0 && (
                <>
                  {/* Headers dos dias da semana */}
                  <div className="grid grid-cols-7 gap-1 sm:gap-2 mb-3 sm:mb-4">
                    {['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'].map(day => (
                      <div key={day} className="text-center font-medium text-muted-foreground text-xs sm:text-sm py-1 sm:py-2">
                        {day}
                      </div>
                    ))}
                  </div>

                  {/* Grid do calendário */}
                  <div className="grid grid-cols-7 gap-1 sm:gap-2">
                    {monthDays.map((day, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.01 }}
                        className={`
                          min-h-16 sm:min-h-20 lg:min-h-24 p-1 sm:p-2 border rounded-lg cursor-pointer transition-all duration-200
                          ${day.isCurrentMonth
                            ? 'bg-background border-border hover:bg-muted/50'
                            : 'bg-muted/20 border-muted'
                          }
                          ${day.isToday ? 'ring-1 sm:ring-2 ring-primary' : ''}
                          ${selectedDate && isSameDay(day.date, selectedDate) ? 'bg-primary/10 border-primary' : ''}
                        `}
                        onClick={() => setSelectedDate(day.date)}
                      >
                        <div className={`
                          font-medium mb-1 text-xs sm:text-sm
                          ${day.isCurrentMonth ? 'text-foreground' : 'text-muted-foreground'}
                          ${day.isToday ? 'text-primary font-bold' : ''}
                        `}>
                          {day.day}
                        </div>
                        
                        {/* Eventos do dia */}
                        <div className="space-y-0.5 sm:space-y-1">
                          {day.events.slice(0, window.innerWidth < 640 ? 1 : 2).map((event, eventIndex) => (
                            <div
                              key={eventIndex}
                              className={`
                                text-xs px-0.5 sm:px-1 py-0.5 sm:py-1 rounded border truncate
                                ${getEventStatusColor(event.status)}
                              `}
                              title={`${event.title} - ${format(event.startTime, 'HH:mm')} às ${format(event.endTime, 'HH:mm')} - ${getEventStatusLabel(event.status)}`}
                            >
                              <div className="font-medium truncate text-xs">{event.title}</div>
                              <div className="text-center opacity-75 text-xs hidden sm:block">
                                {format(event.startTime, 'HH:mm')}
                                {event.status === 'ongoing' && (
                                  <span className="ml-1 animate-pulse">●</span>
                                )}
                              </div>
                            </div>
                          ))}
                          {day.events.length > 2 && (
                            <div className="text-xs text-muted-foreground text-center">
                              +{day.events.length - 2} mais
                            </div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Painel lateral com detalhes */}
        <div className="space-y-4">
          {/* Eventos do dia selecionado */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <CalendarIcon className="h-5 w-5" />
                {selectedDate ? format(selectedDate, "dd 'de' MMMM", { locale: ptBR }) : 'Selecione um dia'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedDateEvents.length === 0 ? (
                <div className="text-center py-8">
                  <CalendarIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Nenhuma aula agendada para este dia
                  </p>
                  {classGroup && selectedDate && (
                    <Button size="sm" className="mt-4" asChild>
                      <Link href={`/turmas/${classGroup.id}/aulas/nova`}>
                        <Plus className="h-4 w-4 mr-1" />
                        Agendar Aula
                      </Link>
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedDateEvents
                    .sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
                    .map((event, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={`
                          p-3 rounded-lg border
                          ${getEventStatusColor(event.status)}
                        `}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getEventStatusIcon(event.status)}
                            <span className="font-medium text-sm">
                              {event.title}
                            </span>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {getEventStatusLabel(event.status)}
                          </Badge>
                        </div>
                        
                        <div className="space-y-1 text-xs">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>
                              {format(event.startTime, 'HH:mm')} - {format(event.endTime, 'HH:mm')}
                            </span>
                          </div>
                          
                          {event.instructor && (
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              <span>{event.instructor}</span>
                            </div>
                          )}
                          
                          {event.location && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              <span>{event.location}</span>
                            </div>
                          )}
                          
                          {event.attendanceCount !== undefined && event.maxCapacity && (
                            <div className="flex items-center gap-1">
                              <UserCheck className="h-3 w-3" />
                              <span>{event.attendanceCount}/{event.maxCapacity} presentes</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex gap-1 mt-3">
                          <Button size="sm" variant="outline" className="h-7 text-xs" asChild>
                            <Link href={`/presenca/${event.id}`}>
                              <Eye className="h-3 w-3 mr-1" />
                              Ver
                            </Link>
                          </Button>
                          {event.status === 'scheduled' && (
                            <Button size="sm" variant="outline" className="h-7 text-xs" asChild>
                              <Link href={`/aulas/editar/${event.id}`}>
                                <Edit3 className="h-3 w-3 mr-1" />
                                Editar
                              </Link>
                            </Button>
                          )}
                        </div>
                      </motion.div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Estatísticas rápidas */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Estatísticas do Mês</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total de Aulas</span>
                  <Badge variant="secondary">{events.length}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Concluídas</span>
                  <Badge variant="outline" className="text-green-600">
                    {events.filter(e => e.status === 'completed').length}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Agendadas</span>
                  <Badge variant="outline" className="text-blue-600">
                    {events.filter(e => e.status === 'scheduled').length}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Canceladas</span>
                  <Badge variant="outline" className="text-red-600">
                    {events.filter(e => e.status === 'cancelled').length}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 